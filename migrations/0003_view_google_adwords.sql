-- Migration number: 0003 	 2025-07-20T09:57:36.200Z
DROP VIEW IF EXISTS view_google_adwords;

CREATE VIEW view_google_adwords AS
WITH counts AS (
  SELECT 
    DATE(datetime(created_at, '-4 hours')) AS date,
    COUNT(CASE WHEN status != 'PREQUAL_DENIED' THEN 1 END) AS approved,
    COUNT(CASE WHEN status = 'PREQUAL_DENIED' THEN 1 END) AS denied
  FROM applications
  WHERE json_extract(utm, '$.utm_source') = 'GoogleAdwords'
  GROUP BY DATE(datetime(created_at, '-4 hours'))
)
SELECT 
  date,
  approved,
  denied,
  CAST(
    100.0 * approved / NULLIF(approved + denied, 0)
    AS INTEGER
  ) || '%' AS approved_percent
FROM counts
ORDER BY date DESC;
