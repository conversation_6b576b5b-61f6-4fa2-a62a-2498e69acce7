DROP VIEW IF EXISTS recent_apps;

CREATE VIEW recent_apps AS
SELECT
  id,
  uuid,
  status,
  created_at,
  json_extract(agent, '$.name') AS agent_name,

  json_extract(preQualifyFields, '$.businessName')       AS businessName,
  json_extract(preQualifyFields, '$.fundingAmount')      AS fundingAmount,
  json_extract(preQualifyFields, '$.estimatedFICO')      AS estimatedFICO,
  json_extract(preQualifyFields, '$.purpose')            AS purpose,
  json_extract(preQualifyFields, '$.topPriority')        AS topPriority,
  json_extract(preQualifyFields, '$.timeline')           AS timeline,
  json_extract(preQualifyFields, '$.monthlyRevenue')     AS monthlyRevenue,
  json_extract(preQualifyFields, '$.businessStartDate')  AS businessStartDate,
  json_extract(preQualifyFields, '$.email')              AS email,

  salesforce_id,
  bank_stmts,

  fastTrack,

  json_extract(utm, '$.utm_rep')      AS utm_rep,
  json_extract(utm, '$.utm_source')   AS utm_source,
  json_extract(utm, '$.utm_campaign') AS utm_campaign,
  json_extract(utm, '$.utm_site')     AS utm_site,
  json_extract(utm, '$.utm_af')       AS utm_af,
  json_extract(utm, '$.utm_referrer') AS utm_referrer,
  CAST(json_extract(utm, '$.utm_dur') AS INTEGER)      AS utm_dur
FROM applications
ORDER BY created_at DESC;
