import { SELF } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, afterAll, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import * as pandadocStatus from '../pandadoc-status.js';
import { generateCreateAppRequest, generateApplicationData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe.skip('Sign App Handler', () => {
  const ogSalesforce = SendToQueue.salseforce;
  const ogEmail = SendToQueue.email;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();
    SendToQueue.email = vi.fn();
    pandadocStatus.isPandaDocSigned = vi.fn().mockResolvedValue(true);
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
    SendToQueue.email = ogEmail;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('signs valid submitted application - sets status to APP_SIGNED', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    expect(createResponse.status).toBe(201);
    const createData = await createResponse.json();

    // Start the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    // Submit the application with application fields
    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    SendToQueue.salseforce = vi.fn().mockClear();
    SendToQueue.email = vi.fn().mockClear();

    // Sign the application
    const signResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(signResponse.status).toBe(200);

    const signData = await signResponse.json();
    expect(signData.data.status).toBe('APP_SIGNED');
    expect(signData.data.uuid).toBe(createData.uuid);
    expect(signData.data.signed_at).toBeDefined();

    expect(SendToQueue.salseforce).toHaveBeenCalledTimes(1);
    expect(SendToQueue.email).toHaveBeenCalledTimes(1);
  });

  it('rejects signing application with invalid status', async () => {
    // First create an approved application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Try to sign the application without submitting it first
    const signResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(signResponse.status).toBe(400);

    const errorData = await signResponse.json();
    expect(errorData.error).toContain("Application can't be signed");
  });

  it('rejects signing when document is not signed (non-test environment)', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Start and submit the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    // Mock isPandaDocSigned to return false
    pandadocStatus.isPandaDocSigned = vi.fn().mockResolvedValueOnce(false);

    // Mock import.meta.env to simulate non-test environment
    const originalEnv = import.meta.env;
    Object.defineProperty(import.meta, 'env', {
      value: { MODE: 'production' },
      writable: true,
    });

    const signResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    // Restore original env
    Object.defineProperty(import.meta, 'env', {
      value: originalEnv,
      writable: true,
    });

    expect(signResponse.status).toBe(400);

    const errorData = await signResponse.json();
    expect(errorData.error).toContain('Document is not yet signed');
  });

  it('handles database errors gracefully', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Start and submit the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    // Mock ApplicationD1.update to throw an error
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const signResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(signResponse.status).toBe(500);

    const errorData = await signResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });

  it('handles PandaDoc status check errors gracefully', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Start and submit the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    // Mock isPandaDocSigned to throw an error
    pandadocStatus.isPandaDocSigned = vi.fn().mockRejectedValueOnce(new Error('PandaDoc API error'));

    const signResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(signResponse.status).toBe(500);

    const errorData = await signResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });
});
