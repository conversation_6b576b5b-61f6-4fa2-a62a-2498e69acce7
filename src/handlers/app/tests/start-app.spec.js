import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, afterAll, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { generateCreateAppRequest } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

const generateFastTrackPrequalData = (overrides = {}) => ({
  businessName: 'FastTrack Business LLC',
  firstName: 'Fast',
  lastName: 'Track',
  email: '<EMAIL>',
  phone: '1234567890',
  estimatedFICO: '700-780',
  consent: true,
  ...overrides,
});

const generateFastTrackRequest = (overrides = {}) => ({
  preQualifyFields: generateFastTrackPrequalData(),
  domain: 'app.pinnaclefunding.com',
  utm: { utm_rep: 'dev' },
  ...overrides,
});

describe('Start App Handler', () => {
  const ogSaleforce = SendToQueue.salseforce;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSaleforce;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('starts valid applications - set status: APP_STARTED', async () => {
    const [approvedApp, fastTrackApp] = await Promise.all([
      SELF.fetch(`${BASE_URL}/app`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(generateCreateAppRequest()),
      }),
      SELF.fetch(`${BASE_URL}/app/fasttrack`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(generateFastTrackRequest()),
      }),
    ]);

    expect(approvedApp.status).toBe(201);
    expect(fastTrackApp.status).toBe(201);

    const [approvedData, fastTrackData] = await Promise.all([approvedApp.json(), fastTrackApp.json()]);

    SendToQueue.salseforce = vi.fn().mockClear();

    const [approvedStart, fastTrackStart] = await Promise.all([
      SELF.fetch(`${BASE_URL}/app/${approvedData.uuid}/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      }),
      SELF.fetch(`${BASE_URL}/app/${fastTrackData.uuid}/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      }),
    ]);

    expect(approvedStart.status).toBe(200);
    expect(fastTrackStart.status).toBe(200);

    const [approvedStartData, fastTrackStartData] = await Promise.all([approvedStart.json(), fastTrackStart.json()]);

    expect(approvedStartData.data.status).toBe('APP_STARTED');
    expect(fastTrackStartData.data.status).toBe('APP_STARTED');

    expect(SendToQueue.salseforce).toHaveBeenCalledTimes(2);
  });

  it('rejects starting application with invalid status', async () => {
    // First create an approved application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Manually update the application status to something invalid
    await ApplicationD1.update(env, createData.uuid, { status: 'APP_SUBMITTED' });

    // Try to start the application
    const startResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(startResponse.status).toBe(400);

    const errorData = await startResponse.json();
    expect(errorData.error).toContain("Application can't be started");
  });

  it('handles database errors gracefully', async () => {
    // First create an approved application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Mock ApplicationD1.update to throw an error
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const startResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(startResponse.status).toBe(500);

    const errorData = await startResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });
});
