import { SELF } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, afterAll, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import * as pandadoc from '../../../pandadoc.js';
import { generateCreateAppRequest, generateApplicationData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe.skip('Edit App Handler', () => {
  const ogSalesforce = SendToQueue.salseforce;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();
    pandadoc.moveDocumentToDraft = vi.fn().mockResolvedValue({ id: 'mock-document-id', status: 'document.draft' });
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('edits valid submitted application - sets status to APP_EDITING', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    expect(createResponse.status).toBe(201);
    const createData = await createResponse.json();

    // Start the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    // Submit the application with application fields
    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    SendToQueue.salseforce = vi.fn().mockClear();

    // Edit the application
    const editResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/edit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(editResponse.status).toBe(200);

    const editData = await editResponse.json();
    expect(editData.data.status).toBe('APP_EDITING');
    expect(editData.data.uuid).toBe(createData.uuid);
    expect(editData.data.edited_at).toBeDefined();

    expect(SendToQueue.salseforce).toHaveBeenCalledTimes(1);
    expect(pandadoc.moveDocumentToDraft).toHaveBeenCalledTimes(1);
  });

  it('rejects editing application with invalid status', async () => {
    // First create an approved application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Try to edit the application without submitting it first
    const editResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/edit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(editResponse.status).toBe(400);

    const errorData = await editResponse.json();
    expect(errorData.error).toContain("Application can't be edited");
  });

  it('handles database errors gracefully', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Start and submit the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    // Mock ApplicationD1.update to throw an error
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const editResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/edit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(editResponse.status).toBe(500);

    const errorData = await editResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });

  it('handles PandaDoc errors gracefully', async () => {
    // First create and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Start and submit the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    // Mock moveDocumentToDraft to throw an error
    pandadoc.moveDocumentToDraft = vi.fn().mockRejectedValueOnce(new Error('PandaDoc API error'));

    const editResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/edit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(editResponse.status).toBe(500);

    const errorData = await editResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });
});
