import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, afterAll, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import * as pandadoc from '../../../pandadoc.js';
import { generateCreateAppRequest, generateApplicationData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe('Submit App Handler', () => {
  const ogSalesforce = SendToQueue.salseforce;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();
    pandadoc.createPandaDocSession = vi.fn().mockResolvedValue({
      document: { id: 'mock-document-id', status: 'document.draft' },
      session: { id: 'mock-session-id', url: 'https://mock-session-url.com' },
    });
    pandadoc.updatePandaDocFields = vi.fn().mockResolvedValue();
    pandadoc.sendPandaDocSilently = vi.fn().mockResolvedValue();
    pandadoc.createEmbeddedSession = vi.fn().mockResolvedValue({
      id: 'mock-session-id',
      url: 'https://mock-session-url.com',
    });
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('submits application from APP_STARTED status - creates PandaDoc and sets status to APP_SUBMITTED', async () => {
    // First create and start an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    expect(createResponse.status).toBe(201);
    const createData = await createResponse.json();

    // Start the application
    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    SendToQueue.salseforce = vi.fn().mockClear();

    // Submit the application
    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    const submitResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    expect(submitResponse.status).toBe(200);

    const submitData = await submitResponse.json();
    expect(submitData.data).toMatchObject({
      uuid: createData.uuid,
      status: 'APP_SUBMITTED',
      submitted_at: expect.any(String),
      pandadoc: expect.objectContaining({
        document: expect.objectContaining({
          id: 'mock-document-id',
        }),
        session: expect.objectContaining({
          id: 'mock-session-id',
        }),
      }),
    });

    // Verify database was updated
    const savedApp = await ApplicationD1.get(env, createData.uuid);
    expect(savedApp.status).toBe('APP_SUBMITTED');
    expect(savedApp.submitted_at).toBeDefined();
    expect(savedApp.applicationFields).toMatchObject(generateApplicationData());

    // Verify PandaDoc functions were called
    expect(pandadoc.createPandaDocSession).toHaveBeenCalledTimes(1);
    expect(SendToQueue.salseforce).toHaveBeenCalledTimes(1);
  });

  it('submits application from APP_EDITING status - updates PandaDoc and creates embedded session', async () => {
    // First create, start, and submit an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: generateApplicationData() }),
    });

    // Set application to editing status
    await ApplicationD1.update(env, createData.uuid, { status: 'APP_EDITING' });

    SendToQueue.salseforce = vi.fn().mockClear();

    // Submit again with updated data
    const updatedApplicationData = generateApplicationData({
      businessName: 'Updated Business Name LLC',
    });
    const resubmitRequest = {
      applicationFields: updatedApplicationData,
    };
    const resubmitResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(resubmitRequest),
    });

    expect(resubmitResponse.status).toBe(200);

    const resubmitData = await resubmitResponse.json();
    expect(resubmitData.data.status).toBe('APP_SUBMITTED');

    // Verify PandaDoc update functions were called for editing flow
    expect(pandadoc.updatePandaDocFields).toHaveBeenCalledTimes(1);
    expect(pandadoc.sendPandaDocSilently).toHaveBeenCalledTimes(1);
    expect(pandadoc.createEmbeddedSession).toHaveBeenCalledTimes(1);
    expect(SendToQueue.salseforce).toHaveBeenCalledTimes(1);
  });

  it('rejects submission with invalid status', async () => {
    // First create an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    // Try to submit without starting (invalid status)
    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    const submitResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    expect(submitResponse.status).toBe(400);

    const errorData = await submitResponse.json();
    expect(errorData.error).toContain("Application can't be submitted");
  });

  it('handles database errors gracefully', async () => {
    // First create and start an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    // Mock ApplicationD1.update to throw an error
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    const submitResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    expect(submitResponse.status).toBe(500);

    const errorData = await submitResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });

  it('handles PandaDoc errors gracefully', async () => {
    // First create and start an application
    const createRequest = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(createRequest),
    });

    const createData = await createResponse.json();

    await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    // Mock createPandaDocSession to throw an error
    pandadoc.createPandaDocSession = vi.fn().mockRejectedValueOnce(new Error('PandaDoc API error'));

    const submitRequest = {
      applicationFields: generateApplicationData(),
    };
    const submitResponse = await SELF.fetch(`${BASE_URL}/app/${createData.uuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitRequest),
    });

    expect(submitResponse.status).toBe(500);

    const errorData = await submitResponse.json();
    expect(errorData.error).toBe('Internal Server Error');
    expect(errorData.errorId).toBeDefined();
  });
});
