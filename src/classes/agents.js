import { MetaD1 } from './meta';

const AGENTS_KEY = 'agents:list';
export class Agents {
  static async getAgents(env) {
    const agentsList = (await MetaD1.get(env, AGENTS_KEY)) || [];
    return agentsList;
  }

  static async setAgents(env, agentsList) {
    if (!Array.isArray(agentsList)) {
      throw new Error('Agents list must be an array');
    }

    await MetaD1.set(env, AGENTS_KEY, agentsList);
    return agentsList;
  }

  static async updateAgent(env, agentId, fields) {
    const agents = await this.getAgents(env);
    const index = agents.findIndex((a) => a.id === agentId);
    if (index === -1) throw new Error('Agent not found');

    agents[index] = { ...agents[index], ...fields };
    await this.setAgents(env, agents);
    const agent = agents[index];
    return { agent, index };
  }

  static async findAgent(env, username = '') {
    if (username === 'test') return env.TEST_AGENT;
    const agents = await this.getAgents(env);
    const match = agents.find(({ email }) => email.split('@')[0].toLowerCase() === username.toLowerCase());
    if (!match) return null;
    return this.formatAgent(match);
  }

  static formatAgent(agent) {
    // only return necessary fields from the agent object
    const { id, name, phone, email, image, calendlyUrl } = agent;
    return {
      id,
      name,
      phone,
      email,
      image,
      calendlyUrl,
      assigned_at: new Date().toISOString(),
    };
  }
}
